import logging
import time
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

from autogen_core import CancellationToken
from autogen_core.memory import Memory, MemoryContent, MemoryMimeType, MemoryQueryResult, UpdateContextResult
from autogen_core.model_context import <PERSON><PERSON><PERSON>ompletionContext
from autogen_core.models import ChatCompletionClient, SystemMessage, UserMessage
from autogen_ext.memory.chromadb import ChromaDBVectorMemory, PersistentChromaDBVectorMemoryConfig, HttpChromaDBVectorMemoryConfig
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

# 使用纯内存模式的ChromaDB客户端
try:
    import chromadb
    import tempfile
except ImportError:
    pass  # 将在运行时检查


class MemoryLevel(str):
    """Memory level enumeration for hierarchical memory"""
    SHORT_TERM = "short_term"
    MEDIUM_TERM = "medium_term"
    LONG_TERM = "long_term"


class ChromaDBHierarchicalMemoryConfig(BaseModel):
    """Configuration for hierarchical (short/medium/long-term) memory using ChromaDB."""

    # Add model_config to allow arbitrary types
    model_config = {
        "arbitrary_types_allowed": True
    }

    # Base ChromaDB configuration
    collection_name: str = Field(
        default="hierarchical_memory",
        description="Base name for ChromaDB collections (will be suffixed with level)"
    )
    persistence_path: Optional[str] = Field(
        default=None,
        description="Path to store ChromaDB data (for persistent storage)"
    )
    host: Optional[str] = Field(
        default=None,
        description="Host for remote ChromaDB server (if using HTTP)"
    )
    port: Optional[int] = Field(
        default=None,
        description="Port for remote ChromaDB server (if using HTTP)"
    )

    # Time settings (in seconds)
    short_to_medium_threshold: int = Field(
        default=24 * 60 * 60,  # 1 day
        description="Time threshold (seconds) for promoting short-term to medium-term"
    )
    medium_to_long_threshold: int = Field(
        default=7 * 24 * 60 * 60,  # 7 days
        description="Time threshold (seconds) for promoting medium-term to long-term"
    )

    # Query settings
    short_term_k: int = Field(default=5, description="Number of short-term memories to retrieve")
    medium_term_k: int = Field(default=3, description="Number of medium-term memories to retrieve")
    long_term_k: int = Field(default=2, description="Number of long-term memories to retrieve")
    short_term_score_threshold: float = Field(default=0.4, description="Minimum similarity score for short-term memories")
    medium_term_score_threshold: float = Field(default=0.5, description="Minimum similarity score for medium-term memories")
    long_term_score_threshold: float = Field(default=0.6, description="Minimum similarity score for long-term memories")

    # Memory compression settings
    enable_memory_compression: bool = Field(
        default=False,
        description="Whether to enable memory compression when promoting memories"
    )
    llm_client: Optional[ChatCompletionClient] = Field(
        default=None,
        description="LLM client for memory compression (required if enable_memory_compression=True)"
    )

    # Compression prompt templates
    short_to_medium_compression_template: str = Field(
        default="""
        Please compress the following short-term memory content into a more concise medium-term representation.
        Keep key information, facts, and insights while removing redundant details.
        This information needs to remain relevant for days to weeks.

        Short-term memory content:
        {content}

        Please output the compressed medium-term memory:
        """,
        description="Template for short-term to medium-term memory compression"
    )

    medium_to_long_compression_template: str = Field(
        default="""
        Please distill the following medium-term memory content into a long-term memory form.
        Keep only the most essential, core information and facts in the most concise way possible.
        This information will be stored as key knowledge for the user over the long term.

        Medium-term memory content:
        {content}

        Please output the distilled long-term memory:
        """,
        description="Template for medium-term to long-term memory compression"
    )

    # Memory cluster compression template
    memory_cluster_compression_template: str = Field(
        default="""
        Please summarize and compress the following set of related memories.
        These memories belong to the same topic or related domain and need to have their core ideas extracted and redundancies removed.

        Memory content collection:
        {content_list}

        Please output a concise summary containing all key information:
        """,
        description="Template for compressing clusters of related memories"
    )

    # Maintenance settings
    maintenance_interval: int = Field(
        default=100,
        description="Number of operations between maintenance runs (0 to disable automatic maintenance)"
    )


class ChromaDBHierarchicalMemory(Memory):
    """
    Hierarchical memory implementation (short-term, medium-term, and long-term) using ChromaDB.

    This implementation extends the ChromaDBVectorMemory approach to create a three-tier memory model
    similar to human memory systems:
    - Short-term memory: recent or important content
    - Medium-term memory: content from days or weeks ago
    - Long-term memory: persistent and core content

    This implementation automatically:
    1. Categorizes memories into different levels based on time and importance
    2. Promotes memories between levels based on age and relevance
    3. Optionally compresses memories when promoting to higher levels
    4. Retrieves the most relevant memories from each level during conversations

    Args:
        config (ChromaDBHierarchicalMemoryConfig): Configuration for the hierarchical memory system
    """

    def __init__(self, config: ChromaDBHierarchicalMemoryConfig) -> None:
        """Initialize the hierarchical memory system"""
        self._config = config
        self._operation_count = 0

        # Validate compression settings
        if config.enable_memory_compression and config.llm_client is None:
            raise ValueError("LLM client must be provided when memory compression is enabled")

        # Initialize ChromaDB memory stores for each level
        self._short_term_memory = self._create_memory_store(MemoryLevel.SHORT_TERM)
        self._medium_term_memory = self._create_memory_store(MemoryLevel.MEDIUM_TERM)
        self._long_term_memory = self._create_memory_store(MemoryLevel.LONG_TERM)

    def _create_memory_store(self, level: str) -> ChromaDBVectorMemory:
        """Create a ChromaDB memory store for the specified memory level"""
        collection_name = f"{self._config.collection_name}_{level}"

        # Configure parameters based on level
        if level == MemoryLevel.SHORT_TERM:
            k = self._config.short_term_k
            score_threshold = self._config.short_term_score_threshold
        elif level == MemoryLevel.MEDIUM_TERM:
            k = self._config.medium_term_k
            score_threshold = self._config.medium_term_score_threshold
        elif level == MemoryLevel.LONG_TERM:
            k = self._config.long_term_k
            score_threshold = self._config.long_term_score_threshold
        else:
            raise ValueError(f"Unknown memory level: {level}")

        # 使用内存模式进行测试
        if self._config.persistence_path == ":memory:":
            # 直接创建内存中的ChromaDB实例，避免文件系统权限问题
            try:
                config = PersistentChromaDBVectorMemoryConfig(
                    collection_name=collection_name,
                    k=k,
                    score_threshold=score_threshold,
                    client=chromadb.EphemeralClient()  # 使用纯内存客户端
                )
                return ChromaDBVectorMemory(config=config)
            except Exception as e:
                logger.error(f"创建内存ChromaDB失败: {e}")
                # 回退到临时目录
                temp_dir = tempfile.gettempdir()
                logger.info(f"回退到临时目录: {temp_dir}")
                config = PersistentChromaDBVectorMemoryConfig(
                    collection_name=collection_name,
                    persistence_path=temp_dir,
                    k=k,
                    score_threshold=score_threshold
                )
                return ChromaDBVectorMemory(config=config)

        # Create appropriate config based on persistence type
        if self._config.host and self._config.port:
            # Use HTTP config for remote ChromaDB
            config = HttpChromaDBVectorMemoryConfig(
                collection_name=collection_name,
                host=self._config.host,
                port=self._config.port,
                k=k,
                score_threshold=score_threshold
            )
        else:
            # Use persistent config for local ChromaDB
            config = PersistentChromaDBVectorMemoryConfig(
                collection_name=collection_name,
                persistence_path=self._config.persistence_path,
                k=k,
                score_threshold=score_threshold
            )

        return ChromaDBVectorMemory(config=config)

    async def _determine_memory_level(self, metadata: Dict[str, Any]) -> str:
        """Determine memory level based on metadata and time"""
        current_time = time.time()
        created_time = metadata.get("created_at", current_time)
        importance = float(metadata.get("importance", 0.5))

        # Check if level is explicitly specified
        if "memory_level" in metadata:
            return metadata["memory_level"]

        # Calculate age (seconds)
        age = current_time - created_time

        # Adjust thresholds based on importance (important memories stay in short-term longer)
        short_threshold = self._config.short_to_medium_threshold * (1 + importance)
        medium_threshold = self._config.medium_to_long_threshold * (1 + importance)

        if age < short_threshold:
            return MemoryLevel.SHORT_TERM
        elif age < medium_threshold:
            return MemoryLevel.MEDIUM_TERM
        else:
            return MemoryLevel.LONG_TERM

    async def _compress_memory(self, content: str, level: str) -> str:
        """Compress memory content based on memory level

        Args:
            content: The memory content to compress
            level: The memory level ("short_term" or "medium_term")

        Returns:
            Compressed memory content
        """
        if not self._config.llm_client:
            return content

        try:
            if level == "short_term":
                # Compress short-term to medium-term
                prompt = self._config.short_to_medium_compression_template.format(content=content)
            elif level == "medium_term":
                # Compress medium-term to long-term
                prompt = self._config.medium_to_long_compression_template.format(content=content)
            else:
                # Unknown level, return as-is
                return content

            # Use LLM for compression
            messages = [
                SystemMessage(content="You are a helpful assistant that compresses memories for long-term storage."),
                UserMessage(content=prompt, source="user") # Add source field
            ]

            response = await self._config.llm_client.create(messages=messages)

            if response and hasattr(response, "content"):
                compressed = response.content

                # Ensure not empty or too short (could indicate an error)
                if compressed and len(compressed) > (len(content) // 10):  # At least 10% of original length
                    return compressed

            # If something went wrong, return original
            return content
        except Exception as e:
            logger.error(f"Error in memory compression: {e}")
            return content

    async def compress_memory_cluster(self, memories: List[MemoryContent]) -> str:
        """
        Compress a set of related memories into a single summary.

        Args:
            memories: List of related memories to compress

        Returns:
            Compressed summary content
        """
        if not self._config.enable_memory_compression or not self._config.llm_client:
            # If compression not enabled, return concatenated content
            if not memories:
                return ""
            return "\n".join([str(m.content) for m in memories])

        # Extract content list
        content_list = "\n".join([f"- {str(m.content)}" for m in memories])

        # Use compression template from config
        prompt = self._config.memory_cluster_compression_template.format(
            content_list=content_list
        )

        try:
            # Call LLM for compression
            response = await self._config.llm_client.create(
                messages=[UserMessage(content=prompt, source="user")]
            )

            # Extract response content, adapting to different response types
            if hasattr(response, 'content'):
                compressed = response.content
            else:
                compressed = str(response)

            if isinstance(compressed, str):
                return compressed.strip()
            else:
                logger.warning(f"Unexpected response type from LLM cluster compression: {type(compressed)}")
                # Return concatenated content on failure
                return "\n".join([str(m.content) for m in memories])
        except Exception as e:
            logger.error(f"Memory cluster compression failed: {e}")
            # Return concatenated content on failure
            return "\n".join([str(m.content) for m in memories])

    def update_compression_template(self, template_type: str, new_template: str) -> None:
        """
        Update the compression prompt template of specified type.

        Args:
            template_type: Template type ('short_to_medium', 'medium_to_long', or 'cluster')
            new_template: New template content, should contain {content} or {content_list} placeholders
        """
        if template_type == 'short_to_medium':
            self._config.short_to_medium_compression_template = new_template
        elif template_type == 'medium_to_long':
            self._config.medium_to_long_compression_template = new_template
        elif template_type == 'cluster':
            self._config.memory_cluster_compression_template = new_template
        else:
            raise ValueError(f"Unknown template type: {template_type}")

        logger.info(f"Updated compression template: {template_type}")

    async def add(
        self,
        content: MemoryContent,
        cancellation_token: Optional[CancellationToken] = None,
    ) -> None:
        """
        Add memory content to the appropriate memory level.

        Args:
            content: Memory content to add
            cancellation_token: Optional cancellation token
        """
        if content.metadata is None:
            content.metadata = {}

        # Add a unique memory_id if not present
        if "memory_id" not in content.metadata:
            import uuid
            content.metadata["memory_id"] = str(uuid.uuid4())

        # Add timestamp if not present
        if "created_at" not in content.metadata:
            content.metadata["created_at"] = time.time()

        # Determine memory level (if not specified)
        if "memory_level" not in content.metadata:
            memory_level = await self._determine_memory_level(content.metadata)
            content.metadata["memory_level"] = memory_level
        else:
            memory_level = content.metadata["memory_level"]

        # Add to appropriate memory store
        if memory_level == MemoryLevel.SHORT_TERM:
            await self._short_term_memory.add(content, cancellation_token)
        elif memory_level == MemoryLevel.MEDIUM_TERM:
            await self._medium_term_memory.add(content, cancellation_token)
        elif memory_level == MemoryLevel.LONG_TERM:
            await self._long_term_memory.add(content, cancellation_token)
        else:
            raise ValueError(f"Unknown memory level: {memory_level}")

        # Increment operation count and perform maintenance if needed
        self._operation_count += 1
        if self._config.maintenance_interval > 0 and self._operation_count >= self._config.maintenance_interval:
            await self._perform_maintenance()
            self._operation_count = 0

    async def _perform_maintenance(self, query_text: Optional[str] = "memory") -> None:
        """执行记忆维护，包括提升和压缩记忆"""
        used_query_text = query_text if query_text is not None else "memory"
        logger.info(f"Performing memory maintenance using query: '{used_query_text}'")

        # 获取当前时间
        current_time = time.time()

        # --- Short-term to Medium-term Promotion ---
        try:
            short_promotion_cutoff = current_time - self._config.short_to_medium_threshold

            # Query short-term memories eligible for promotion based on time
            short_promotion_cutoff = current_time - self._config.short_to_medium_threshold
            # 查询短期记忆，尝试使用通用文本、大结果数和时间过滤器
            short_promotion_cutoff = current_time - self._config.short_to_medium_threshold
            # 查询短期记忆，使用查询文本和时间过滤器
            short_promotion_cutoff = current_time - self._config.short_to_medium_threshold
            short_term_candidates = await self._short_term_memory.query(
                used_query_text, # 使用实际的查询文本
                where={"created_at": {"$lt": short_promotion_cutoff}} # 过滤出符合时间的记忆
            )

            promotable_memories = []
            if short_term_candidates and short_term_candidates.results:
                for memory in short_term_candidates.results:
                    if memory.metadata and memory.metadata.get("created_at", current_time) < short_promotion_cutoff:
                         # Retrieve original mime_type from metadata, default to TEXT if missing
                        original_mime_type = memory.metadata.get("mime_type", MemoryMimeType.TEXT)
                        # Reconstruct MemoryContent with original mime type for processing
                        promotable_memories.append(MemoryContent(
                            content=memory.content,
                            mime_type=original_mime_type,
                            metadata=memory.metadata
                        ))

            logger.info(f"Found {len(promotable_memories)} short-term memories eligible for promotion.")

            for memory in promotable_memories:
                if not memory.metadata: continue

                logger.info(f"Promoting memory to medium-term: {str(memory.content)[:30]}...")

                content_str = str(memory.content)
                compressed_content = content_str

                if self._config.enable_memory_compression and self._config.llm_client:
                    try:
                        compressed_content = await self._compress_memory(content_str, "short_term")
                        logger.info("Memory compressed successfully")
                    except Exception as e:
                        logger.error(f"Memory compression failed: {e}")
                        compressed_content = content_str

                new_metadata = dict(memory.metadata)
                new_metadata["memory_level"] = "medium_term"
                new_metadata["promoted_at"] = current_time
                if compressed_content != content_str:
                    new_metadata["compressed_from"] = content_str

                # Use the string value corresponding to MemoryMimeType.TEXT
                medium_memory = MemoryContent(
                    content=compressed_content,
                    mime_type=MemoryMimeType.TEXT, # Use the enum member directly, assuming it resolves to "text" or is handled
                    metadata=new_metadata
                )

                await self._medium_term_memory.add(medium_memory)

                # Deletion is removed as ChromaDBVectorMemory lacks a delete method
                # and direct collection delete caused inconsistencies.
                # memory_id_to_delete = memory.metadata.get("memory_id")
                # if memory_id_to_delete:
                #     logger.info(f"Skipping deletion of {memory_id_to_delete} from short-term due to wrapper limitations.")

        except Exception as e:
            logger.exception(f"Error during short-term promotion: {e}")

        # --- Medium-term to Long-term Promotion ---
        try:
            medium_promotion_cutoff = current_time - self._config.medium_to_long_threshold

            # Query medium-term memories eligible for promotion based on time
            medium_promotion_cutoff = current_time - self._config.medium_to_long_threshold
            # 查询中期记忆，尝试使用通用文本、大结果数和时间过滤器
            medium_promotion_cutoff = current_time - self._config.medium_to_long_threshold
            # 查询中期记忆，使用查询文本和时间过滤器
            medium_promotion_cutoff = current_time - self._config.medium_to_long_threshold
            medium_term_candidates = await self._medium_term_memory.query(
                used_query_text, # 使用实际的查询文本
                where={"created_at": {"$lt": medium_promotion_cutoff}} # 过滤出符合时间的记忆
            )

            promotable_medium_memories = []
            if medium_term_candidates and medium_term_candidates.results:
                 for memory in medium_term_candidates.results:
                     if memory.metadata and memory.metadata.get("created_at", current_time) < medium_promotion_cutoff:
                         # Retrieve original mime_type from metadata, default to TEXT if missing
                        original_mime_type = memory.metadata.get("mime_type", MemoryMimeType.TEXT)
                        # Reconstruct MemoryContent with original mime type for processing
                        promotable_medium_memories.append(MemoryContent(
                            content=memory.content,
                            mime_type=original_mime_type,
                            metadata=memory.metadata
                        ))

            logger.info(f"Found {len(promotable_medium_memories)} medium-term memories eligible for promotion.")

            for memory in promotable_medium_memories:
                if not memory.metadata: continue

                logger.info(f"Promoting memory to long-term: {str(memory.content)[:30]}...")

                content_str = str(memory.content)
                compressed_content = content_str

                if self._config.enable_memory_compression and self._config.llm_client:
                    try:
                        compressed_content = await self._compress_memory(content_str, "medium_term")
                        logger.info("Memory compressed successfully")
                    except Exception as e:
                        logger.error(f"Memory compression failed: {e}")
                        compressed_content = content_str

                new_metadata = dict(memory.metadata)
                new_metadata["memory_level"] = "long_term"
                new_metadata["promoted_at"] = current_time
                if compressed_content != content_str:
                    new_metadata["compressed_from"] = content_str

                # Use the original mime_type when creating the new MemoryContent
                long_memory = MemoryContent(
                    content=compressed_content,
                    mime_type=MemoryMimeType.TEXT, # 强制设置为 MemoryMimeType.TEXT
                    metadata=new_metadata
                )

                await self._long_term_memory.add(long_memory)

                # Deletion is removed as ChromaDBVectorMemory lacks a delete method
                # and direct collection delete caused inconsistencies.
                # memory_id_to_delete = memory.metadata.get("memory_id")
                # if memory_id_to_delete:
                #     logger.info(f"Skipping deletion of {memory_id_to_delete} from medium-term due to wrapper limitations.")

        except Exception as e:
            logger.exception(f"Error during medium-term promotion: {e}")

        # 更新操作计数
        self._operation_count += 1

    async def query(
        self,
        query: str | MemoryContent,
        memory_level: Optional[str] = None,
        cancellation_token: Optional[CancellationToken] = None,
        **kwargs: Any,
    ) -> MemoryQueryResult:
        """
        Query memory content, optionally limited to a specific memory level.

        Args:
            query: Query text or MemoryContent object
            memory_level: Optional memory level restriction
            cancellation_token: Optional cancellation token
            **kwargs: Additional keyword arguments including:
                - metadata_filter: Dictionary of metadata key-value pairs to filter results
                - k: Number of results to return
                - where: ChromaDB where filter

        Returns:
            MemoryQueryResult containing matching memories
        """
        query_text = str(query.content) if isinstance(query, MemoryContent) else str(query)

        # Extract metadata filter if present
        metadata_filter = kwargs.get("metadata_filter", None)

        # If metadata_filter is present, convert it to a where clause for ChromaDB
        if metadata_filter:
            # Create a where clause for each metadata key-value pair
            where_clause = {}
            for key, value in metadata_filter.items():
                where_clause[key] = {"$eq": value}

            # Add or update the where clause in kwargs
            if "where" in kwargs:
                # Combine with existing where clause
                existing_where = kwargs["where"]
                # Merge the dictionaries
                for key, value in where_clause.items():
                    existing_where[key] = value
            else:
                # Set the where clause
                kwargs["where"] = where_clause

        if memory_level:
            # Query specific level
            if memory_level == MemoryLevel.SHORT_TERM:
                return await self._short_term_memory.query(query_text, cancellation_token, **kwargs)
            elif memory_level == MemoryLevel.MEDIUM_TERM:
                return await self._medium_term_memory.query(query_text, cancellation_token, **kwargs)
            elif memory_level == MemoryLevel.LONG_TERM:
                return await self._long_term_memory.query(query_text, cancellation_token, **kwargs)
            else:
                raise ValueError(f"Unknown memory level: {memory_level}")
        else:
            # Query all levels and merge results, but skip levels with n_results=0
            # Extract n_results values for each level (convert k to n_results)
            short_term_k = kwargs.get("k", kwargs.get("n_results", self._config.short_term_k))
            medium_term_k = kwargs.get("k", kwargs.get("n_results", self._config.medium_term_k))
            long_term_k = kwargs.get("k", kwargs.get("n_results", self._config.long_term_k))

            # Query short term (always query if k > 0 or if it's the only level with data)
            if short_term_k > 0 or (medium_term_k == 0 and long_term_k == 0):
                short_term_kwargs = kwargs.copy()
                # Remove k if it exists and use n_results
                short_term_kwargs.pop("k", None)
                if short_term_k > 0:
                    short_term_kwargs["n_results"] = short_term_k
                else:
                    # If it's the only level, use a reasonable default
                    short_term_kwargs["n_results"] = max(1, self._config.short_term_k)
                short_term_result = await self._short_term_memory.query(query_text, cancellation_token, **short_term_kwargs)
            else:
                short_term_result = MemoryQueryResult(results=[])

            # Query medium term only if k > 0
            if medium_term_k > 0:
                medium_term_kwargs = kwargs.copy()
                medium_term_kwargs.pop("k", None)
                medium_term_kwargs["n_results"] = medium_term_k
                medium_term_result = await self._medium_term_memory.query(query_text, cancellation_token, **medium_term_kwargs)
            else:
                medium_term_result = MemoryQueryResult(results=[])

            # Query long term only if k > 0
            if long_term_k > 0:
                long_term_kwargs = kwargs.copy()
                long_term_kwargs.pop("k", None)
                long_term_kwargs["n_results"] = long_term_k
                long_term_result = await self._long_term_memory.query(query_text, cancellation_token, **long_term_kwargs)
            else:
                long_term_result = MemoryQueryResult(results=[])

            # Merge results (with deduplication)
            all_results = []
            seen_ids = set()

            # Collect all results, avoiding duplicates by memory_id in metadata
            for memory in (short_term_result.results + medium_term_result.results + long_term_result.results):
                memory_id = memory.metadata.get("memory_id", "") if memory.metadata else ""
                if memory_id and memory_id in seen_ids:
                    continue
                if memory_id:
                    seen_ids.add(memory_id)
                all_results.append(memory)

            # Sort by score (if available)
            all_results.sort(
                key=lambda x: float(x.metadata.get("score", 0)) if x.metadata else 0,
                reverse=True
            )

            # Get requested number of results (default to sum of k values from all levels)
            k = kwargs.get("k", self._config.short_term_k + self._config.medium_term_k + self._config.long_term_k)
            all_results = all_results[:k]

            return MemoryQueryResult(results=all_results)

    async def update_context(
        self,
        model_context: ChatCompletionContext,
        where: Optional[Dict[str, Any]] = None,
    ) -> UpdateContextResult:
        """
        Update model context with relevant memory content from all memory levels.

        Args:
            model_context: Model context to update
            where: Optional ChromaDB where filter to filter memories

        Returns:
            UpdateContextResult containing memories used to update the context
        """
        messages = await model_context.get_messages()
        if not messages:
            return UpdateContextResult(memories=MemoryQueryResult(results=[]))

        # Extract query from last message
        last_message = messages[-1]
        query_text = last_message.content if isinstance(last_message.content, str) else str(last_message)

        # Prepare query kwargs with where filter if provided
        query_kwargs = {}
        if where:
            query_kwargs["where"] = where

        # Query each memory level with the where filter
        short_term_results = await self.query(query_text, memory_level=MemoryLevel.SHORT_TERM, **query_kwargs)
        medium_term_results = await self.query(query_text, memory_level=MemoryLevel.MEDIUM_TERM, **query_kwargs)
        long_term_results = await self.query(query_text, memory_level=MemoryLevel.LONG_TERM, **query_kwargs)

        # Merge all results for return
        all_results = short_term_results.results + medium_term_results.results + long_term_results.results

        if all_results:
            # Format results by memory level
            memory_sections = []

            # Short-term memories (recent context)
            if short_term_results.results:
                short_term_strings = [f"• {str(memory.content)}" for memory in short_term_results.results]
                memory_sections.append("RECENT CONTEXT:\n" + "\n".join(short_term_strings))

            # Medium-term memories
            if medium_term_results.results:
                medium_term_strings = [f"• {str(memory.content)}" for memory in medium_term_results.results]
                memory_sections.append("RELEVANT BACKGROUND:\n" + "\n".join(medium_term_strings))

            # Long-term memories (key knowledge)
            if long_term_results.results:
                long_term_strings = [f"• {str(memory.content)}" for memory in long_term_results.results]
                memory_sections.append("IMPORTANT KNOWLEDGE:\n" + "\n".join(long_term_strings))

            # Combine memory sections
            memory_context = "MEMORY RECALL:\n\n" + "\n\n".join(memory_sections)

            # Add to context
            await model_context.add_message(SystemMessage(content=memory_context))

        # Return all results
        return UpdateContextResult(memories=MemoryQueryResult(results=all_results))

    async def get_message_count(self) -> int:
        """
        Attempts to retrieve the highest message_count from all memory levels.
        Currently, this method returns 0 due to difficulties in reliably accessing
        all metadata from the underlying ChromaDBVectorMemory instances without
        modifying the autogen_ext library.
        """
        logger.warning(
            f"ChromaDBHierarchicalMemory.get_message_count for collection base '{self._config.collection_name}': "
            f"Reliable retrieval of persistent message count is currently not supported due to limitations "
            f"with the underlying ChromaDBVectorMemory wrapper's query parameter handling or collection access. "
            f"This method will return 0. Message counting will be session-based in the calling agent if it relies on this."
        )
        # Returning 0 as a fallback. The agent should ideally use its own in-memory counter
        # or this method needs to be fixed if a truly persistent cross-session message_count is required
        # and the underlying ChromaDBVectorMemory allows for reliable full metadata retrieval.
        return 0
    async def clear(self, memory_level: Optional[str] = None) -> None:
        """
        Clear memory content, optionally limited to a specific memory level.

        Args:
            memory_level: Optional memory level to clear
        """

        if memory_level:
            # Clear specific level
            if memory_level == MemoryLevel.SHORT_TERM:
                await self._short_term_memory.clear()
            elif memory_level == MemoryLevel.MEDIUM_TERM:
                await self._medium_term_memory.clear()
            elif memory_level == MemoryLevel.LONG_TERM:
                await self._long_term_memory.clear()
            else:
                raise ValueError(f"Unknown memory level: {memory_level}")
        else:
            # Clear all memory
            await self._short_term_memory.clear()
            await self._medium_term_memory.clear()
            await self._long_term_memory.clear()

        logger.info(f"Cleared memories: {'all levels' if memory_level is None else memory_level}")

    async def close(self) -> None:
        """Clean up resources"""
        try:
            await self._short_term_memory.close()
            await self._medium_term_memory.close()
            await self._long_term_memory.close()
            logger.info("ChromaDBHierarchicalMemory closed and resources cleaned up")
        except Exception as e:
            logger.error(f"Error closing ChromaDBHierarchicalMemory: {e}")