"""
定义教学助手 Agent (TeachingAssistantAgent) 类，该类继承自 AssistantAgent。

该 Agent 负责：
- 接收用户在企业微信 (WeCom) 上发送的关于编程教学的问题
- 调用 LLM 模型生成教学内容以及回复学生问题
- 智能判断是否需要调用爬虫抓取题目信息
- 记录和读取用户对话历史，提供个性化教学

核心业务流程：
- 接收来自用户的编程问题，主要针对编程竞赛题目答疑
- 智能判断是否需要调用 crawler_engineer_agent 抓取题目信息（如洛谷题目 P1005、P1560 等）
- 如需抓取，将题目信息附加到用户查询后记录到用户记忆中
- 查询最近3轮对话记录作为上下文生成回复
- 记录用户和助手的对话到用户专属记忆空间
"""

import logging
import re
import os
import sys
import asyncio
import time
from datetime import datetime
from typing import Optional, Dict, Any, List
# import chromadb
# from chromadb.config import Settings

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.tools import AgentTool
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import CancellationToken
from autogen_core.memory import MemoryContent, MemoryMimeType
from autogen_core.models import SystemMessage, UserMessage

# 使用相对导入以适应不同的Python路径设置
from config import get_config
from data_models import ContentType
from agents.dev_team.crawler_engineer_agent import CrawlerEngineerAgent, ParseType
from utils.memory.multi_user_memory import MultiUserMemory, MultiUserMemoryConfig

# 配置日志
logger = logging.getLogger(__name__)

class TeachingAssistantAgent(AssistantAgent):
    """教学助手代理，负责回答学生关于编程竞赛题目的问题

    特点：
    1. 智能判断是否需要调用爬虫抓取题目信息
    2. 记录和查询用户对话历史
    3. 提供个性化的编程教学指导
    """

    def __init__(
        self,
        name: str = "teaching_assistant",
        system_message: str = """你是一位编程教师，负责回答学生的编程问题。

你的工作原则：
1. 简洁明了地回答问题，不做无关扩展
2. 确保每条回复不超过2048字节（约600-700中文字）
3. 使用正常、专业的语气，避免过度使用emoji或儿童化语言
4. 解释概念时使用清晰的比喻，但保持专业性
5. 提供简单易懂的代码示例，避免过于复杂的实现

回答格式要求：
1. 使用纯文本格式回复，不要使用Markdown格式
2. 不要使用```代码块标记，直接使用缩进和空行来展示代码
3. 不要使用粗体、斜体等Markdown格式，使用普通文本
4. 使用数字和字母编号来组织内容，如"1."、"2."、"a."、"b."等
5. 使用空行分隔段落，使文本易于阅读

回答编程问题时：
- 直接切入主题，简明扼要地解释核心概念
- 使用简单的类比帮助理解，但不过度简化
- 提供符合题目要求的伪代码或简单代码示例（使用缩进而非代码块标记）
- 分步骤解释时保持简洁
- 如果问题复杂，优先解释最关键的部分

当学生提到特定题目编号（如P1005）时，你会获取题目信息，并用清晰的语言解释题目要求。

记住：学生需要专业、清晰的指导，而非过度简化的解释。学生通过微信查看你的回复，微信不支持Markdown格式，所以必须使用纯文本格式。""",
        model_client: Optional[OpenAIChatCompletionClient] = None,
        crawler_agent: Optional[CrawlerEngineerAgent] = None,
    ) -> None:
        """初始化教学助手代理

        Args:
            name: 代理名称
            system_message: 系统提示消息
            model_client: 模型客户端，默认使用配置中指定的模型
            crawler_agent: 爬虫工程师代理，用于抓取题目信息
        """
        # 如果未提供模型客户端，则创建默认客户端
        if model_client is None:
            try:
                config = get_config()
                model_client = OpenAIChatCompletionClient(
                    model=config['agents']['teaching_assistant']['agent_model_name'],
                    api_key=config['agents']['teaching_assistant']['agent_model_api_key'],
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )
            except KeyError as e:
                logger.info(f"Configuration key not found: {e}, using default values")
                model_client = OpenAIChatCompletionClient(
                    model="gemini-2.0-flash-exp",
                    api_key=get_config()['api_keys']['gemini_api_key'],
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )

        # 存储模型客户端到实例变量
        self.model_client = model_client

        # 初始化爬虫代理
        self.crawler_agent = crawler_agent if crawler_agent else CrawlerEngineerAgent()

        # 创建爬虫工具
        crawler_tool = AgentTool(agent=self.crawler_agent)

        # 初始化简化的记忆系统
        self.memory = self._init_simple_memory_system()

        # 调用父类初始化
        super().__init__(
            name=name,
            system_message=system_message,
            model_client=model_client,
            tools=[crawler_tool],
            memory=[self.memory],
            reflect_on_tool_use=True
        )

        logger.info(f"TeachingAssistantAgent initialized with name: {name}")

    def _init_simple_memory_system(self):
        """初始化简化的记忆系统（不包含记忆压缩功能）

        Returns:
            MultiUserMemory: 初始化的记忆系统实例
        """
        # 创建记忆系统基础路径
        base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../..", "data", "memories")
        os.makedirs(base_path, exist_ok=True)

        # 创建简化的记忆系统配置（不启用记忆压缩）
        memory_config = MultiUserMemoryConfig(
            base_path=base_path,
            shared_memory_enabled=False,  # 不启用共享记忆
            user_isolation="db_path",  # 使用db_path模式进行用户隔离
            collection_name="teaching_assistant_memory",  # 设置集合名称
            enable_memory_compression=False,  # 禁用记忆压缩
            short_term_k=6,  # 查询最近6条记录（3轮对话）
            medium_term_k=0,  # 不使用中期记忆
            long_term_k=0,   # 不使用长期记忆
            maintenance_interval=1000  # 减少维护频率
        )

        # 创建记忆系统
        memory = MultiUserMemory(memory_config)
        logger.info("Simple memory system initialized (compression disabled)")

        return memory




    async def handle_user_message(self, user_id: str, content: str, image_data: Optional[bytes] = None,
                             image_type: Optional[str] = None, cancellation_token: Optional[CancellationToken] = None) -> str:
        """处理用户消息并生成回复

        Args:
            user_id: 用户ID（企业微信的external_userid）
            content: 用户消息内容
            image_data: 可选的图片数据
            image_type: 可选的图片类型
            cancellation_token: 取消令牌

        Returns:
            str: 回复内容
        """
        logger.info(f"Handling message from user {user_id}: {content[:50]}...")
        if image_data:
            logger.info(f"Message includes image of type: {image_type}")

        # 设置当前用户ID
        self.current_user_id = user_id

        # 处理图片消息
        if image_data:
            if content:
                content = f"{content}\n\n[用户发送了一张图片]"
            else:
                content = "[用户发送了一张图片]"

        # 智能判断是否需要调用爬虫抓取题目信息
        problem_id = self._extract_problem_id(content)
        problem_info = None

        # 如果包含题目编号，抓取题目信息并附加到用户查询
        if problem_id:
            logger.info(f"Detected problem ID: {problem_id}, fetching problem information")
            problem_info = await self._fetch_problem_info(problem_id)
            if problem_info:
                # 将题目信息附加到用户查询后
                problem_text = self._format_problem_info(problem_info)
                content = f"{content}\n\n[题目信息]\n{problem_text}"
                logger.info(f"Appended problem info to user query")

        # 将用户消息（包含可能的题目信息）添加到记忆
        await self._add_to_memory(user_id, content, "user", cancellation_token)

        # 如果是图片消息，返回标准回复
        if "[用户发送了一张图片]" in content:
            response = "我看到您发送了一张图片，但我无法直接查看图片内容。请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。"
            await self._add_to_memory(user_id, response, "assistant", cancellation_token)
            return response

        # 直接查询最近的对话记录，避免复杂的记忆系统
        memory_results = await self._query_recent_conversations(user_id, content)

        # 生成回复
        response = await self._generate_response(user_id, content, memory_results, cancellation_token)

        # 将回复添加到记忆
        await self._add_to_memory(user_id, response, "assistant", cancellation_token)

        return response

    def _format_problem_info(self, problem_info: Dict[str, Any]) -> str:
        """格式化题目信息为文本

        Args:
            problem_info: 题目信息字典

        Returns:
            str: 格式化后的题目信息文本
        """
        if not problem_info:
            return ""

        formatted_text = []

        if "title" in problem_info:
            formatted_text.append(f"题目标题: {problem_info['title']}")

        if "description" in problem_info:
            formatted_text.append(f"题目描述: {problem_info['description']}")

        if "input_format" in problem_info:
            formatted_text.append(f"输入格式: {problem_info['input_format']}")

        if "output_format" in problem_info:
            formatted_text.append(f"输出格式: {problem_info['output_format']}")

        if "examples" in problem_info and problem_info["examples"]:
            formatted_text.append("示例:")
            for i, example in enumerate(problem_info["examples"], 1):
                if "input_example" in example:
                    formatted_text.append(f"  输入{i}: {example['input_example']}")
                if "output_example" in example:
                    formatted_text.append(f"  输出{i}: {example['output_example']}")

        return "\n".join(formatted_text)

    async def _process_image(self, image_data: bytes, image_type: str) -> str:
        """处理图片数据，提取图片中的文本或其他内容

        Args:
            image_data: 图片二进制数据
            image_type: 图片MIME类型

        Returns:
            str: 从图片中提取的文本内容
        """
        try:
            # 这里我们使用一个简单的方法来解析图片内容
            # 在实际应用中，你可能需要使用OCR服务或其他图像处理服务

            # 由于当前版本的autogen_core可能不直接支持图片处理，我们使用一个替代方案
            # 将图片保存到临时文件并使用其他工具处理，或者使用外部API

            import tempfile
            import os

            # 创建临时文件保存图片
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{image_type.split('/')[-1] if '/' in image_type else 'jpg'}") as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name

            try:
                # 这里可以调用OCR服务或其他图像分析服务
                # 例如，可以使用Google Cloud Vision API、Azure Computer Vision等
                # 为了简单起见，我们这里返回一个基本描述

                # 提供更友好的回复，告知用户我们已收到图片但需要他们提供更多信息
                ocr_result = "我看到您发送了一张图片。由于目前我无法直接分析图片内容，请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。"

                # 清理临时文件
                os.unlink(temp_file_path)

                return ocr_result
            except Exception as e:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                raise e

        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            # 提供更友好的错误消息，不暴露技术细节
            return "我在处理您发送的图片时遇到了一些问题。请您描述一下图片中的内容，或者直接输入您的问题，这样我就能更好地帮助您了。"

    def _extract_problem_id(self, content: str) -> Optional[str]:
        """从用户消息中提取题目编号

        Args:
            content: 用户消息内容

        Returns:
            Optional[str]: 题目编号，如果未找到则返回None
        """
        # 匹配洛谷题目编号格式（如P1000, P1005, P1560）
        pattern = r'P\d{4,}'
        match = re.search(pattern, content)
        if match:
            return match.group(0)  # 使用group(0)返回整个匹配
        return None

    def _extract_problem_id_from_memory(self, memory_results: Any) -> Optional[str]:
        """从记忆结果中提取题目编号

        Args:
            memory_results: 记忆查询结果

        Returns:
            Optional[str]: 题目编号，如果未找到则返回None
        """
        if not memory_results or not hasattr(memory_results, 'results') or not memory_results.results:
            return None

        # 遍历记忆结果，寻找包含题目编号的记忆
        for memory in memory_results.results:
            if hasattr(memory, 'content'):
                content = str(memory.content)
                problem_id = self._extract_problem_id(content)
                if problem_id:
                    return problem_id

        return None

    async def _extract_recent_problem_info(self, user_id: str, memory_results: Any) -> Optional[Dict[str, Any]]:
        """从记忆中提取最近讨论的题目信息

        Args:
            user_id: 用户ID
            memory_results: 记忆查询结果

        Returns:
            Optional[Dict[str, Any]]: 题目信息字典，如果未找到则返回None
        """
        try:
            # 首先尝试从当前的记忆结果中提取题目信息
            if memory_results and hasattr(memory_results, 'results') and memory_results.results:
                # 遍历记忆结果，寻找包含题目信息的记忆
                for memory in memory_results.results:
                    if hasattr(memory, 'content'):
                        content = str(memory.content)
                        # 检查是否包含题目编号
                        problem_id = self._extract_problem_id(content)
                        if problem_id:
                            logger.info(f"Found problem ID {problem_id} in memory for user {user_id}")

                            # 检查是否包含题目详细信息（如标题、描述等）
                            if "标题" in content or "title" in content.lower() or "描述" in content or "description" in content.lower():
                                # 尝试构建题目信息字典
                                problem_info = {
                                    "problem_id": problem_id
                                }

                                # 提取标题
                                title_match = re.search(r'标题[：:]\s*(.+?)(?:\n|$)', content)
                                if title_match:
                                    problem_info["title"] = title_match.group(1).strip()

                                # 提取描述
                                desc_match = re.search(r'描述[：:]\s*(.+?)(?:\n|$)', content)
                                if desc_match:
                                    problem_info["description"] = desc_match.group(1).strip()

                                # 如果找到了足够的信息，返回题目信息字典
                                if len(problem_info) > 1:  # 不仅仅只有problem_id
                                    return problem_info

            # 如果当前记忆结果中没有找到足够的信息，尝试专门查询题目信息
            # 首先查询最近讨论的题目编号
            memory_query = "最近讨论的编程题目是什么"
            recent_memories = await self.memory.query(
                query=memory_query,
                user_id=user_id,
                cancellation_token=None
            )

            # 从记忆中提取题目编号
            problem_id = self._extract_problem_id_from_memory(recent_memories)
            if not problem_id:
                return None

            # 查询与该题目相关的详细信息
            problem_query = f"题目 {problem_id} 的详细信息"
            problem_memories = await self.memory.query(
                query=problem_query,
                user_id=user_id,
                cancellation_token=None
            )

            if not problem_memories or not hasattr(problem_memories, 'results') or not problem_memories.results:
                # 如果没有找到详细信息，但至少有题目编号，返回基本信息
                return {"problem_id": problem_id}

            # 构建题目信息字典
            problem_info = {"problem_id": problem_id}

            # 遍历记忆结果，提取题目详细信息
            for memory in problem_memories.results:
                if hasattr(memory, 'content'):
                    content = str(memory.content)

                    # 提取标题
                    title_match = re.search(r'标题[：:]\s*(.+?)(?:\n|$)', content)
                    if title_match and "title" not in problem_info:
                        problem_info["title"] = title_match.group(1).strip()

                    # 提取描述
                    desc_match = re.search(r'描述[：:]\s*(.+?)(?:\n|$)', content)
                    if desc_match and "description" not in problem_info:
                        problem_info["description"] = desc_match.group(1).strip()

                    # 提取输入格式
                    input_match = re.search(r'输入[格式]?[：:]\s*(.+?)(?:\n|$)', content)
                    if input_match and "input_format" not in problem_info:
                        problem_info["input_format"] = input_match.group(1).strip()

                    # 提取输出格式
                    output_match = re.search(r'输出[格式]?[：:]\s*(.+?)(?:\n|$)', content)
                    if output_match and "output_format" not in problem_info:
                        problem_info["output_format"] = output_match.group(1).strip()

            return problem_info

        except Exception as e:
            logger.error(f"Error extracting recent problem info: {str(e)}")
            return None

    async def _retrieve_problem_info(self, problem_id: Optional[str] = None, user_id: Optional[str] = None) -> str:
        """检索特定题目的详细信息

        Args:
            problem_id: 题目编号，如果为None则尝试从记忆中找到最近讨论的题目
            user_id: 用户ID，如果为None则使用当前用户ID

        Returns:
            str: 题目信息的文本描述
        """
        # 获取当前用户ID
        if not user_id:
            # 从上下文中获取用户ID，这里假设在handle_user_message中设置了当前用户ID
            user_id = getattr(self, 'current_user_id', None)
            if not user_id:
                return "错误：未提供用户ID，无法检索记忆"

        # 如果未提供题目编号，尝试从记忆中找到最近讨论的题目
        if not problem_id:
            # 查询用户的记忆，寻找最近讨论的题目
            memory_query = "最近讨论的编程题目编号是什么"
            recent_memories = await self.memory.query(
                query=memory_query,
                user_id=user_id,
                cancellation_token=None
            )
            logger.info(f"Searching for problem ID in memory for user {user_id}")

            # 从记忆中提取题目编号
            problem_id = self._extract_problem_id_from_memory(recent_memories)
            if not problem_id:
                return "未找到最近讨论的题目编号，请提供具体的题目编号"

        # 查询用户的记忆，寻找与该题目相关的信息
        memory_query = f"题目 {problem_id} 的详细信息"
        problem_memories = await self.memory.query(memory_query, user_id)

        if not problem_memories or not hasattr(problem_memories, 'results') or not problem_memories.results:
            return f"抱歉，我没有关于题目 {problem_id} 的详细信息。请提供题目描述，我会尽力帮助解答。"

        # 构建题目信息的文本描述
        problem_info = f"题目 {problem_id}：\n\n"

        # 遍历记忆结果，提取题目信息
        for memory in problem_memories.results:
            if hasattr(memory, 'content'):
                content = str(memory.content)
                if problem_id in content:
                    problem_info += f"{content}\n\n"

        return problem_info

    async def _retrieve_user_history(self, user_id: Optional[str] = None, query: str = "最近的对话记录") -> str:
        """检索用户的历史对话记录

        Args:
            user_id: 用户ID，如果为None则使用当前用户ID
            query: 查询内容，默认为"最近的对话记录"

        Returns:
            str: 用户历史对话记录的文本描述
        """
        # 获取当前用户ID
        if not user_id:
            # 从上下文中获取用户ID，这里假设在handle_user_message中设置了当前用户ID
            user_id = getattr(self, 'current_user_id', None)
            if not user_id:
                return "错误：未提供用户ID，无法检索记忆"

        # 查询用户的记忆
        user_memories = await self.memory.query(
            query=query,
            user_id=user_id,
            cancellation_token=None
        )
        logger.info(f"Retrieved user history for user {user_id}")

        if not user_memories or not hasattr(user_memories, 'results') or not user_memories.results:
            return f"未找到用户 {user_id} 的历史对话记录"

        # 构建用户历史对话记录的文本描述
        history = f"用户 {user_id} 的历史对话记录：\n\n"

        # 遍历记忆结果，提取对话记录
        for i, memory in enumerate(user_memories.results):
            if hasattr(memory, 'content'):
                content = str(memory.content)
                history += f"{i+1}. {content}\n\n"

        return history

    def _extract_problem_info_from_memories(self, memories: List[Any], problem_id: str) -> Optional[Dict[str, Any]]:
        """从记忆中提取题目信息

        Args:
            memories: 记忆列表
            problem_id: 题目编号

        Returns:
            Optional[Dict[str, Any]]: 题目信息字典，如果未找到则返回None
        """
        if not memories:
            return None

        # 初始化题目信息字典
        problem_info = {
            "problem_id": problem_id
        }

        # 遍历记忆，提取题目信息
        for memory in memories:
            if not hasattr(memory, 'content'):
                continue

            content = str(memory.content)

            # 检查是否包含题目信息
            if problem_id in content:
                # 提取标题
                title_match = re.search(r'(?:标题|任务名称|题目)[：:]\s*(.+?)(?:\n|$)', content)
                if title_match and "title" not in problem_info:
                    problem_info["title"] = title_match.group(1).strip()

                # 提取描述
                desc_match = re.search(r'(?:描述|冒险描述)[：:]\s*(.+?)(?:\n|$)', content)
                if desc_match and "description" not in problem_info:
                    problem_info["description"] = desc_match.group(1).strip()

                # 提取输入格式
                input_match = re.search(r'(?:输入格式|魔法输入)[：:]\s*(.+?)(?:\n|$)', content)
                if input_match and "input_format" not in problem_info:
                    problem_info["input_format"] = input_match.group(1).strip()

                # 提取输出格式
                output_match = re.search(r'(?:输出格式|期待结果)[：:]\s*(.+?)(?:\n|$)', content)
                if output_match and "output_format" not in problem_info:
                    problem_info["output_format"] = output_match.group(1).strip()

                # 提取示例（简化处理）
                if "examples" not in problem_info and ("示例" in content or "样例" in content):
                    problem_info["examples"] = []

                    # 简单示例提取
                    input_example = re.search(r'(?:输入|输入宝藏)[：:]\s*(.+?)(?:\n|$)', content)
                    output_example = re.search(r'(?:输出|输出宝藏)[：:]\s*(.+?)(?:\n|$)', content)

                    if input_example and output_example:
                        problem_info["examples"].append({
                            "input_example": input_example.group(1).strip(),
                            "output_example": output_example.group(1).strip()
                        })

        # 如果只有题目ID，没有其他信息，返回None
        if len(problem_info) <= 1:
            return None

        return problem_info

    async def _fetch_problem_info(self, problem_id: str) -> Optional[Dict[str, Any]]:
        """抓取题目信息

        Args:
            problem_id: 题目编号

        Returns:
            Optional[Dict[str, Any]]: 题目信息，如果抓取失败则返回None
        """
        try:
            # 构建洛谷题目URL
            url = f"https://www.luogu.com.cn/problem/{problem_id}"
            logger.info(f"Fetching problem information from {url}")

            # 调用爬虫工程师代理抓取题目信息
            result = await self.crawler_agent._crawl_and_parsing_result(
                target_url=url,
                content_type=ContentType.COMPETITION_TOPIC,
                parse_type=ParseType.ITEM_DETAIL
            )

            if not result:
                logger.warning(f"Failed to fetch problem information for {problem_id}")
                return None

            logger.info(f"Successfully fetched problem information for {problem_id}")
            return result
        except Exception as e:
            logger.error(f"Error fetching problem information for {problem_id}: {str(e)}")
            return None

    async def _add_to_memory(self, user_id: str, content: str, role: str, cancellation_token: Optional[CancellationToken] = None) -> None:
        """将消息添加到用户记忆

        Args:
            user_id: 用户ID
            content: 消息内容
            role: 消息角色（"user"或"assistant"）
            cancellation_token: 取消令牌
        """
        try:
            # 构建记忆元数据
            metadata = {
                "user_id": user_id,
                "role": role,
                "timestamp": datetime.now().isoformat(),
                "created_at": time.time(),
            }

            # 创建记忆内容，确保角色前缀格式一致
            if role == "user" and content.startswith("[user]"):
                formatted_content = content
            elif role == "assistant" and content.startswith("[assistant]"):
                formatted_content = content
            else:
                formatted_content = f"[{role}] {content}"

            memory_content = MemoryContent(
                content=formatted_content,
                mime_type=MemoryMimeType.TEXT,
                metadata=metadata
            )

            # 添加到记忆系统
            await self.memory.add(
                content=memory_content,
                user_id=user_id,
                cancellation_token=cancellation_token
            )
            logger.info(f"Added {role} message to memory for user {user_id}")

        except Exception as e:
            logger.error(f"Error adding message to memory: {str(e)}")



    async def _generate_response(
        self,
        user_id: str,
        user_message: str,
        memory_results: Any,
        cancellation_token: Optional[CancellationToken] = None
    ) -> str:
        """生成回复

        Args:
            user_id: 用户ID
            user_message: 用户消息
            memory_results: 记忆查询结果
            cancellation_token: 取消令牌

        Returns:
            str: 生成的回复
        """
        try:
            # 检查是否是图片消息，如果是，直接返回固定回复，不考虑记忆或上下文
            if "[用户发送了一张图片]" in user_message:
                logger.info(f"Detected image message from user {user_id}, returning standard image response")
                # 清空记忆结果，避免受到之前对话的影响
                if memory_results and hasattr(memory_results, 'results'):
                    # 过滤掉所有包含图片相关内容的记忆
                    filtered_memories = []
                    for memory in memory_results.results:
                        if hasattr(memory, 'content'):
                            content = str(memory.content)
                            if "[用户发送了一张图片]" not in content and "[图片内容]" not in content and "图片" not in content:
                                filtered_memories.append(memory)

                    # 更新记忆结果
                    memory_results.results = filtered_memories
                    logger.info(f"Filtered memory results for image message, remaining: {len(filtered_memories)}")

                # 无论如何，都返回标准图片响应
                return "我看到您发送了一张图片，但我无法直接查看图片内容。请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。"

            # 构建简化的提示
            prompt = self._build_simple_prompt(user_message, memory_results)

            # 获取系统消息（如果没有，则使用默认值）
            system_message = getattr(self, 'system_message', """你是一位C++编程教师，负责回答学生的编程问题。

你的工作原则：
1. 简洁明了地回答问题，不做无关扩展
2. 确保每条回复不超过2048字节（约600-700中文字）
3. 使用正常、专业的语气，避免过度使用emoji或儿童化语言
4. 解释概念时使用清晰的比喻，但保持专业性
5. 提供简单易懂的代码示例，避免过于复杂的实现

回答格式要求：
1. 使用纯文本格式回复，不要使用Markdown格式
2. 不要使用```代码块标记，直接使用缩进和空行来展示代码
3. 不要使用粗体、斜体等Markdown格式，使用普通文本
4. 使用数字和字母编号来组织内容，如"1."、"2."、"a."、"b."等
5. 使用空行分隔段落，使文本易于阅读

回答编程问题时：
- 直接切入主题，简明扼要地解释核心概念
- 使用简单的类比帮助理解，但不过度简化
- 提供符合题目要求的伪代码或简单代码示例（使用缩进而非代码块标记）
- 分步骤解释时保持简洁
- 如果问题复杂，优先解释最关键的部分

当学生提到特定题目编号（如P1005）时，你会获取题目信息，并用清晰的语言解释题目要求。

记住：学生需要专业、清晰的指导，而非过度简化的解释。学生通过微信查看你的回复，微信不支持Markdown格式，所以必须使用纯文本格式。""")

            # 检查model_client是否存在
            if not hasattr(self, 'model_client') or self.model_client is None:
                logger.error("model_client not initialized")
                return "抱歉，系统暂时无法回应。请稍后再试或联系管理员。"

            # 调用LLM生成回复，使用正确的消息格式
            messages = [
                SystemMessage(content=system_message),
                UserMessage(content=prompt, source="user")
            ]

            # 使用正确的方法调用模型
            try:
                # 首选方法是 create
                if hasattr(self.model_client, 'create'):
                    logger.info("Using model_client.create method")
                    response = await self.model_client.create(messages=messages, cancellation_token=cancellation_token)
                # 备选方法是 complete
                elif hasattr(self.model_client, 'complete'):
                    logger.info("Using model_client.complete method")
                    response = await self.model_client.complete(messages=messages, cancellation_token=cancellation_token)
                # 再次备选方法是 chat_completion
                elif hasattr(self.model_client, 'chat_completion'):
                    logger.info("Using model_client.chat_completion method")
                    response = await self.model_client.chat_completion(messages=messages, cancellation_token=cancellation_token)
                # 最后尝试直接调用
                else:
                    logger.info("Trying to call model_client directly")
                    response = await self.model_client(messages=messages, cancellation_token=cancellation_token)

                logger.info(f"Model response type: {type(response)}")
                logger.info(f"Model response attributes: {dir(response) if response else 'None'}")
            except Exception as e:
                logger.error(f"Failed to call model_client: {e}")
                raise ValueError(f"Failed to generate response: {e}")

            if not response or not response.content:
                logger.warning(f"Empty response from LLM for user {user_id}")
                return "抱歉，未能生成有效回复。请重新描述您的问题，我会尽力提供帮助。"

            # 获取响应内容
            response_text = response.content

            # 检查响应长度是否超过2048字节
            response_bytes = len(response_text.encode('utf-8'))
            logger.info(f"Response length: {response_bytes} bytes")

            # 如果超过限制，请求更简洁的回复
            if response_bytes > 2048:
                logger.warning(f"Response exceeds 2048 bytes limit: {response_bytes} bytes")

                # 添加提示要求更简洁的回复
                messages.append(UserMessage(
                    content="你的回复超过了2048字节限制，请提供更简洁的版本，只包含最核心的内容，去除所有不必要的解释和修饰语。",
                    source="user"
                ))

                # 重新生成更简洁的回复
                try:
                    if hasattr(self.model_client, 'create'):
                        simplified_response = await self.model_client.create(messages=messages, cancellation_token=cancellation_token)
                    elif hasattr(self.model_client, 'complete'):
                        simplified_response = await self.model_client.complete(messages=messages, cancellation_token=cancellation_token)
                    elif hasattr(self.model_client, 'chat_completion'):
                        simplified_response = await self.model_client.chat_completion(messages=messages, cancellation_token=cancellation_token)
                    else:
                        simplified_response = await self.model_client(messages=messages, cancellation_token=cancellation_token)

                    response_text = simplified_response.content

                    # 再次检查长度
                    response_bytes = len(response_text.encode('utf-8'))
                    logger.info(f"Simplified response length: {response_bytes} bytes")

                    # 如果仍然超过限制，强制截断
                    if response_bytes > 2048:
                        logger.warning(f"Response still exceeds limit after simplification: {response_bytes} bytes")
                        # 强制截断到安全长度
                        while len(response_text.encode('utf-8')) > 2000:  # 留出余量
                            response_text = response_text[:int(len(response_text) * 0.9)]  # 每次减少10%

                        # 添加截断提示
                        response_text += "\n\n(回复因长度限制被截断)"

                        # 最终长度
                        logger.info(f"Truncated response length: {len(response_text.encode('utf-8'))} bytes")
                except Exception as e:
                    logger.error(f"Error generating simplified response: {e}")
                    # 如果简化失败，直接截断原始响应
                    while len(response_text.encode('utf-8')) > 2000:
                        response_text = response_text[:int(len(response_text) * 0.9)]
                    response_text += "\n\n(回复因长度限制被截断)"

            # 处理响应文本，移除Markdown格式，优化为微信友好的纯文本格式
            response_text = self._format_response_for_wechat(response_text)

            return response_text
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return "抱歉，处理您的请求时出现了问题。请稍后再试或重新提问。"

    def _format_response_for_wechat(self, text: str) -> str:
        """
        将响应文本格式化为微信友好的纯文本格式

        Args:
            text: 原始响应文本

        Returns:
            str: 格式化后的文本
        """
        try:
            # 移除Markdown代码块标记，保留代码内容并添加适当的缩进
            lines = text.split('\n')
            formatted_lines = []
            in_code_block = False
            code_indent = '    '  # 代码块的缩进

            for line in lines:
                # 处理代码块开始和结束标记
                if line.strip().startswith('```'):
                    if in_code_block:
                        # 代码块结束
                        in_code_block = False
                        formatted_lines.append('')  # 添加空行分隔
                    else:
                        # 代码块开始
                        in_code_block = True
                        # 如果代码块标记包含语言信息，添加注释
                        lang = line.strip()[3:].strip()
                        if lang:
                            formatted_lines.append(f"// {lang} 代码:")
                            formatted_lines.append('')  # 添加空行
                    continue

                # 处理代码块内的内容
                if in_code_block:
                    formatted_lines.append(code_indent + line)
                else:
                    # 处理普通文本
                    # 移除Markdown的粗体和斜体标记
                    # line = line.replace('**', '').replace('__', '').replace('*', '').replace('_', '')

                    # 移除Markdown的链接格式，只保留链接文本
                    line = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', line)

                    formatted_lines.append(line)

            # 确保代码块后有空行
            result = '\n'.join(formatted_lines)

            # 移除连续的多个空行，最多保留两个连续空行
            result = re.sub(r'\n{3,}', '\n\n', result)

            # 确保以单个换行符结束
            result = result.rstrip() + '\n'

            return result
        except Exception as e:
            logger.error(f"Error formatting response for WeChat: {str(e)}")
            # 如果格式化失败，返回原始文本
            return text

    def _build_simple_prompt(self, user_message: str, memory_results: Any) -> str:
        """构建简化的回复提示，只包含用户问题和最近3轮对话记录

        Args:
            user_message: 用户消息
            memory_results: 记忆查询结果

        Returns:
            str: 构建的提示
        """
        prompt = f"学生的编程问题：{user_message}\n\n"

        # 添加最近3轮对话记录
        if memory_results and hasattr(memory_results, 'results') and memory_results.results:
            recent_conversations = []

            # 提取对话记录
            for memory in memory_results.results:
                if hasattr(memory, 'content') and hasattr(memory, 'metadata'):
                    content = str(memory.content)
                    role = memory.metadata.get('role') if memory.metadata else None
                    created_at = memory.metadata.get('created_at', 0) if memory.metadata else 0

                    # 确定角色
                    if role == "user" or "[user]" in content:
                        role = "user"
                        content = content.replace("[user] ", "")
                    elif role == "assistant" or "[assistant]" in content:
                        role = "assistant"
                        content = content.replace("[assistant] ", "")
                    else:
                        continue  # 跳过无法识别角色的记录

                    recent_conversations.append({
                        'role': role,
                        'content': content,
                        'created_at': created_at
                    })

            # 按时间排序，最新的在前
            recent_conversations.sort(key=lambda x: x['created_at'], reverse=True)

            # 只取最近3轮对话（最多6条记录：3个用户+3个助手）
            if recent_conversations:
                prompt += "最近的对话记录：\n"
                count = 0
                for conv in recent_conversations[:6]:  # 最多6条记录
                    if count >= 6:  # 限制最多6条
                        break
                    role_name = "用户" if conv['role'] == "user" else "助手"
                    content = conv['content'][:100] + "..." if len(conv['content']) > 100 else conv['content']
                    prompt += f"{role_name}: {content}\n"
                    count += 1
                prompt += "\n"

        prompt += "请根据以上信息回答编程问题，要求简洁明了，不超过2048字节。\n"
        return prompt

    def _get_default_system_message(self) -> str:
        """获取默认的系统消息"""
        return """你是一位编程教师，负责回答学生的编程问题。

你的工作原则：
1. 简洁明了地回答问题，不做无关扩展
2. 确保每条回复不超过2048字节（约600-700中文字）
3. 使用正常、专业的语气，避免过度使用emoji或儿童化语言
4. 解释概念时使用清晰的比喻，但保持专业性
5. 提供简单易懂的代码示例，避免过于复杂的实现

回答格式要求：
1. 使用纯文本格式回复，不要使用Markdown格式
2. 不要使用```代码块标记，直接使用缩进和空行来展示代码
3. 不要使用粗体、斜体等Markdown格式，使用普通文本
4. 使用数字和字母编号来组织内容，如"1."、"2."、"a."、"b."等
5. 使用空行分隔段落，使文本易于阅读

回答编程问题时：
- 直接切入主题，简明扼要地解释核心概念
- 使用简单的类比帮助理解，但不过度简化
- 提供符合题目要求的伪代码或简单代码示例（使用缩进而非代码块标记）
- 分步骤解释时保持简洁
- 如果问题复杂，优先解释最关键的部分

当学生提到特定题目编号（如P1005）时，你会获取题目信息，并用清晰的语言解释题目要求。

记住：学生需要专业、清晰的指导，而非过度简化的解释。学生通过微信查看你的回复，微信不支持Markdown格式，所以必须使用纯文本格式。"""

    def _extract_response_content(self, response: Any) -> str:
        """从LLM响应中提取内容"""
        if hasattr(response, 'content') and response.content:
            return response.content
        elif hasattr(response, 'choices') and response.choices:
            choice = response.choices[0]
            if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                return choice.message.content
            else:
                return str(choice)
        else:
            return str(response)

    def _limit_response_length(self, response_text: str) -> str:
        """限制回复长度，确保不超过2048字节"""
        response_bytes = len(response_text.encode('utf-8'))
        if response_bytes > 2048:
            logger.warning(f"Response exceeds 2048 bytes limit: {response_bytes} bytes, truncating...")
            # 截断到2000字节以内，确保不破坏UTF-8编码
            response_text = response_text.encode('utf-8')[:2000].decode('utf-8', errors='ignore')
            response_text += "..."
        return response_text

    async def _query_recent_conversations(self, user_id: str, current_query: str) -> Any:
        """通过现有的记忆系统查询最近的对话记录

        Args:
            user_id: 用户ID
            current_query: 当前查询内容

        Returns:
            包含最近对话记录的结果对象
        """
        try:
            # 使用现有的记忆系统，但只查询短期记忆
            user_memory_instance = self.memory._get_user_memory(user_id)

            # 尝试获取最近的记忆，使用简单的查询

            # 直接从短期记忆中获取最近的记录
            try:
                # 查询短期记忆，使用正确的参数格式
                short_term_result = await user_memory_instance._short_term_memory.query(
                    "recent conversation",  # query 作为第一个位置参数
                    cancellation_token=None,
                    n_results=6  # 最多6条记录，使用 n_results 而不是 k
                )

                if short_term_result and hasattr(short_term_result, 'results'):
                    # 按时间排序
                    sorted_results = sorted(
                        short_term_result.results,
                        key=lambda x: x.metadata.get('created_at', 0) if x.metadata else 0,
                        reverse=True
                    )

                    # 只保留对话记录
                    conversation_results = []
                    for result in sorted_results:
                        if result.metadata and result.metadata.get('role') in ['user', 'assistant']:
                            conversation_results.append(result)

                    # 限制到最近6条
                    conversation_results = conversation_results[:6]

                    logger.info(f"Found {len(conversation_results)} recent conversations for user {user_id}")

                    # 创建结果对象
                    class SimpleMemoryResult:
                        def __init__(self, results):
                            self.results = results

                    return SimpleMemoryResult(conversation_results)

            except Exception as e:
                logger.warning(f"Error querying short-term memory: {e}")

            # 如果查询失败，返回空结果
            return self._create_empty_memory_result()

        except Exception as e:
            logger.error(f"Error querying recent conversations for user {user_id}: {e}")
            return self._create_empty_memory_result()

    def _create_empty_memory_result(self) -> Any:
        """创建空的记忆查询结果"""
        class EmptyMemoryResult:
            def __init__(self):
                self.results = []

        return EmptyMemoryResult()

    def _create_memory_result_from_conversations(self, conversations: List[Dict[str, Any]]) -> Any:
        """从对话记录创建记忆查询结果"""
        class MemoryItem:
            def __init__(self, content: str, metadata: Dict[str, Any]):
                self.content = content
                self.metadata = metadata

        class MemoryResult:
            def __init__(self, conversations: List[Dict[str, Any]]):
                self.results = []
                for conv in conversations:
                    self.results.append(MemoryItem(conv['content'], conv['metadata']))

        return MemoryResult(conversations)


# 单元测试代码
if __name__ == "__main__" and os.environ.get('RUN_EXAMPLE'):
    import unittest
    from unittest.mock import patch, MagicMock, AsyncMock

    class TeachingAssistantAgentTest(unittest.TestCase):
        @patch('autogen_ext.models.openai.OpenAIChatCompletionClient')
        @patch('agents.dev_team.crawler_engineer_agent.CrawlerEngineerAgent')
        @patch('utils.memory.multi_user_memory.MultiUserMemory')
        def setUp(self, mock_memory, mock_crawler, mock_client):
            # 设置模拟对象
            self.mock_memory = mock_memory.return_value
            # 不再需要模拟_set_compression_prompt，因为我们已经处理了这种情况
            self.mock_memory.add = AsyncMock()
            self.mock_memory.query = AsyncMock(return_value=MagicMock(results=[]))

            self.mock_crawler = mock_crawler.return_value
            self.mock_crawler._crawl_and_parsing_result = AsyncMock(return_value={
                "title": "测试题目",
                "problem_id": "P1000",
                "description": "这是一个测试题目描述",
                "input_format": "输入格式",
                "output_format": "输出格式",
                "examples": [
                    {
                        "input_example": "示例输入",
                        "output_example": "示例输出",
                        "explanation": "示例解释"
                    }
                ]
            })

            self.mock_client = mock_client.return_value
            self.mock_client.acomplete = AsyncMock(return_value=MagicMock(content="这是一个测试回复"))

            # 创建代理实例，但使用patch来避免实际初始化记忆系统
            with patch.object(TeachingAssistantAgent, '_init_memory_system'):
                self.agent = TeachingAssistantAgent(
                    name="teaching_assistant",
                    model_client=self.mock_client,
                    crawler_agent=self.mock_crawler
                )
                # 手动设置记忆系统
                self.agent.memory = self.mock_memory

            # 设置日志
            logging.basicConfig(level=logging.DEBUG)

        def test_extract_problem_id(self):
            # 测试提取题目编号
            self.assertEqual(self.agent._extract_problem_id("请帮我解答P1000这道题"), "P1000")
            self.assertEqual(self.agent._extract_problem_id("我在做P1234，遇到了问题"), "P1234")
            self.assertIsNone(self.agent._extract_problem_id("这道题很难"))
            self.assertIsNone(self.agent._extract_problem_id("P123太短了"))  # 不匹配太短的ID

        async def test_fetch_problem_info(self):
            # 测试抓取题目信息
            result = await self.agent._fetch_problem_info("P1000")
            self.assertIsNotNone(result)
            self.assertEqual(result["title"], "测试题目")
            self.assertEqual(result["problem_id"], "P1000")

            # 测试异常情况
            self.mock_crawler._crawl_and_parsing_result = AsyncMock(side_effect=Exception("测试异常"))
            result = await self.agent._fetch_problem_info("P1000")
            self.assertIsNone(result)

        async def test_add_to_memory(self):
            # 测试添加记忆
            await self.agent._add_to_memory("test_user", "测试消息", "user")
            self.mock_memory.add.assert_called_once()

            # 验证调用参数
            args, _ = self.mock_memory.add.call_args
            self.assertEqual(args[1], "test_user")  # 验证用户ID
            self.assertIn("测试消息", str(args[0].content))  # 验证消息内容

        async def test_handle_user_message(self):
            # 测试处理用户消息
            response = await self.agent.handle_user_message("test_user", "请帮我解答P1000")
            self.assertEqual(response, "这是一个测试回复")

            # 验证调用
            self.mock_memory.add.assert_called()
            self.mock_memory.query.assert_called_once()
            self.mock_crawler._crawl_and_parsing_result.assert_called_once()
            self.mock_client.acomplete.assert_called_once()

        def test_build_simple_prompt(self):
            # 测试构建简化提示
            memory_results = MagicMock()
            memory_results.results = [
                MagicMock(content="[user] 用户问题1", metadata=MagicMock(role="user", created_at=1000)),
                MagicMock(content="[assistant] 助手回答1", metadata=MagicMock(role="assistant", created_at=1001))
            ]

            prompt = self.agent._build_simple_prompt("测试问题", memory_results)

            # 验证提示内容
            self.assertIn("测试问题", prompt)
            self.assertIn("用户问题1", prompt)
            self.assertIn("助手回答1", prompt)

    # 运行单元测试
    if sys.platform != 'win32' and not os.environ.get('RUN_EXAMPLE'):  # 避免在Windows上运行asyncio测试
        unittest.main()

# 示例代码：使用TeachingAssistantAgent处理多用户场景
if __name__ == "__main__":
    import shutil

    async def run_teaching_assistant_example():
        """运行教学助手多用户示例"""
        print("初始化教学助手代理...")

        try:
            # 获取配置
            config = get_config()
            print(f"获取配置成功: {config.keys()}")

            # 创建模型客户端
            print("创建模型客户端...")
            model_client = OpenAIChatCompletionClient(
                model=config['agents']['memory_system']['agent_model_name'],
                api_key=config['agents']['memory_system']['agent_model_api_key'],
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                }
            )
            print("模型客户端创建成功")

            # 初始化爬虫工程师代理
            print("初始化爬虫工程师代理...")
            crawler_agent = CrawlerEngineerAgent()
            print("爬虫工程师代理初始化成功")

            # 初始化教学助手代理
            print("初始化教学助手代理...")
            teaching_assistant = TeachingAssistantAgent(
                name="teaching_assistant_example",
                model_client=model_client,
                crawler_agent=crawler_agent
            )
            print("教学助手代理初始化成功")

            # 直接设置model_client属性，以确保它存在
            if not hasattr(teaching_assistant, 'model_client') or teaching_assistant.model_client is None:
                print("手动设置model_client属性")
                teaching_assistant.model_client = model_client

            # 定义三个不同的用户
            users = {
                "user_p1005": {
                    "id": "example_user_001",
                    "questions": [
                        "你能帮我解答洛谷P1005这道题吗？我不太理解题目要求。",
                        "这道题目的核心算法是什么？",
                        "可以按照题目的描述，一步一步给我讲解下解题思路吗？",
                        "如何处理这道题中的大数问题？",
                        "最后，你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                },
                "user_p1003": {
                    "id": "example_user_002",
                    "questions": [
                        "请帮我分析一下洛谷P1010这道题。",
                        "这道题目需要用什么数据结构？",
                        "如何优化算法复杂度？",
                        "有没有类似的题目可以练习？",
                        "最后，你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                },
                "user_dfs": {
                    "id": "example_user_003",
                    "questions": [
                        "Python中深度优先搜索算法怎么实现？",
                        "DFS和BFS有什么区别？",
                        "如何避免DFS中的栈溢出问题？",
                        "结合上面我们交流的内容，你能帮我解答洛谷P1032这道题吗？",
                        "最后，你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                }
            }

            # 交替处理每个用户的问题，模拟多用户同时交流
            for question_index in range(5):  # 每个用户有4个问题
                for user_type, user_info in users.items():
                    user_id = user_info["id"]
                    question = user_info["questions"][question_index]

                    print(f"\n用户 {user_id} (类型: {user_type}) - 问题 {question_index+1}: {question}")
                    print("-" * 50)

                    try:
                        # 使用教学助手处理问题
                        response = await teaching_assistant.handle_user_message(
                            user_id=user_id,
                            content=question
                        )

                        print(f"教学助手回复:\n{response}")
                    except Exception as e:
                        print(f"处理问题时出错: {e}")
                        import traceback
                        traceback.print_exc()

                    print("=" * 80)

            # 清理记忆数据库路径
            print("\n清理记忆数据库...")
            try:
                # 获取记忆系统基础路径
                base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../..", "data", "memories")

                # 检查路径是否存在
                if os.path.exists(base_path):
                    # 列出所有用户目录
                    for user_dir in os.listdir(base_path):
                        user_path = os.path.join(base_path, user_dir)
                        if os.path.isdir(user_path):
                            print(f"删除用户记忆目录: {user_path}")
                            shutil.rmtree(user_path)

                    print("记忆数据库清理完成")
                else:
                    print(f"记忆基础路径不存在: {base_path}")
            except Exception as e:
                print(f"清理记忆数据库时出错: {e}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"初始化或运行过程中出错: {e}")
            import traceback
            traceback.print_exc()

    # 运行示例
    print("运行教学助手多用户示例...")
    asyncio.run(run_teaching_assistant_example())
